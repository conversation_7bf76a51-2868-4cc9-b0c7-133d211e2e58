import { useQuery } from '@tanstack/react-query';
import { analyticsApi } from '@/lib/api';
import { OrderDashboardStats } from '@/types/api';

// Query keys
export const analyticsKeys = {
  all: ['analytics'] as const,
  dashboard: () => [...analyticsKeys.all, 'dashboard'] as const,
  userStats: () => [...analyticsKeys.all, 'user-stats'] as const,
};

// Get dashboard statistics
export const useDashboardStats = () => {
  return useQuery({
    queryKey: analyticsKeys.dashboard(),
    queryFn: () => analyticsApi.getDashboardStats(),
    staleTime: 1 * 60 * 1000, // 1 minute
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Get user statistics
export const useUserStats = () => {
  return useQuery({
    queryKey: analyticsKeys.userStats(),
    queryFn: () => analyticsApi.getUserStats(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};
