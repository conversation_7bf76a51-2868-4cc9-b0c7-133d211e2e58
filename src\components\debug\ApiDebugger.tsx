'use client';

import React, { useState } from 'react';
import { Button } from '@/components/ui/Button';
import { servicesApi, categoriesApi, apiClient, getTokens } from '@/lib/api';

export default function ApiDebugger() {
  const [results, setResults] = useState<any>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const testEndpoint = async (name: string, apiCall: () => Promise<any>) => {
    setLoading(true);
    setError(null);
    try {
      console.log(`Testing ${name}...`);
      const result = await apiCall();
      console.log(`${name} result:`, result);
      setResults({ name, result, success: true });
    } catch (err: any) {
      console.error(`${name} error:`, err);
      setError(`${name}: ${err.message}`);
      setResults({ name, error: err, success: false });
    } finally {
      setLoading(false);
    }
  };

  const tests = [
    {
      name: 'Auth Tokens',
      test: () => Promise.resolve(getTokens())
    },
    {
      name: 'API Base URL',
      test: () => Promise.resolve({ baseURL: apiClient.defaults.baseURL })
    },
    {
      name: 'Services API',
      test: () => servicesApi.getServices({ limit: 5 })
    },
    {
      name: 'Categories API',
      test: () => categoriesApi.getCategories({ limit: 5 })
    },
    {
      name: 'Raw Services Call',
      test: () => apiClient.get('/catalogue/services/?limit=5')
    },
    {
      name: 'Raw Categories Call',
      test: () => apiClient.get('/catalogue/categories/?limit=5')
    },
    {
      name: 'Test Auth Profile',
      test: () => apiClient.get('/auth/profile/')
    }
  ];

  return (
    <div className="bg-white p-6 rounded-lg shadow space-y-4">
      <h3 className="text-lg font-medium text-gray-900">API Debugger</h3>
      
      <div className="grid grid-cols-1 md:grid-cols-3 gap-2">
        {tests.map((test) => (
          <Button
            key={test.name}
            variant="outline"
            size="sm"
            onClick={() => testEndpoint(test.name, test.test)}
            disabled={loading}
          >
            Test {test.name}
          </Button>
        ))}
      </div>

      {loading && (
        <div className="text-blue-600">Testing API...</div>
      )}

      {error && (
        <div className="bg-red-50 border border-red-200 rounded p-3">
          <div className="text-red-800 font-medium">Error:</div>
          <div className="text-red-600 text-sm">{error}</div>
        </div>
      )}

      {results && (
        <div className="bg-gray-50 border rounded p-3">
          <div className="font-medium text-gray-900 mb-2">
            {results.name} - {results.success ? 'Success' : 'Failed'}
          </div>
          <pre className="text-xs text-gray-600 overflow-auto max-h-64">
            {JSON.stringify(results.success ? results.result : results.error, null, 2)}
          </pre>
        </div>
      )}
    </div>
  );
}
