import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { servicesApi } from '@/lib/api';
import { Service, PaginatedResponse } from '@/types/api';

// Query keys
export const serviceKeys = {
  all: ['services'] as const,
  lists: () => [...serviceKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...serviceKeys.lists(), params] as const,
  details: () => [...serviceKeys.all, 'detail'] as const,
  detail: (serviceId: number) => [...serviceKeys.details(), serviceId] as const,
};

// Get services list with filters
export const useServices = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: serviceKeys.list(params),
    queryFn: () => servicesApi.getServices(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single service details
export const useService = (serviceId: number) => {
  return useQuery({
    queryKey: serviceKeys.detail(serviceId),
    queryFn: () => servicesApi.getServiceDetail(serviceId),
    enabled: !!serviceId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Create service mutation
export const useCreateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData | any) => servicesApi.createService(data),
    onSuccess: () => {
      // Invalidate and refetch services list
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      toast.success('Service created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create service');
    },
  });
};

// Update service mutation
export const useUpdateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceId, data }: { serviceId: number; data: FormData | any }) =>
      servicesApi.updateService(serviceId, data),
    onSuccess: (_, variables) => {
      // Invalidate and refetch services list
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      // Invalidate specific service detail
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.serviceId) });
      toast.success('Service updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update service');
    },
  });
};

// Delete service mutation
export const useDeleteService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (serviceId: number) => servicesApi.deleteService(serviceId),
    onSuccess: (_, serviceId) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.removeQueries({ queryKey: serviceKeys.detail(serviceId) });
      toast.success('Service deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete service');
    },
  });
};

// Bulk update services mutation
export const useBulkUpdateServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceIds, data }: { serviceIds: number[]; data: any }) =>
      servicesApi.bulkUpdateServices(serviceIds, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      // Invalidate specific service details
      variables.serviceIds.forEach(serviceId => {
        queryClient.invalidateQueries({ queryKey: serviceKeys.detail(serviceId) });
      });
      toast.success(`${variables.serviceIds.length} services updated successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update services');
    },
  });
};

// Bulk delete services mutation
export const useBulkDeleteServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (serviceIds: number[]) => servicesApi.bulkDeleteServices(serviceIds),
    onSuccess: (_, serviceIds) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      // Remove specific service details from cache
      serviceIds.forEach(serviceId => {
        queryClient.removeQueries({ queryKey: serviceKeys.detail(serviceId) });
      });
      toast.success(`${serviceIds.length} services deleted successfully`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete services');
    },
  });
};

// Toggle service status mutation
export const useToggleServiceStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ serviceId, isActive }: { serviceId: number; isActive: boolean }) =>
      servicesApi.toggleServiceStatus(serviceId, isActive),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      queryClient.invalidateQueries({ queryKey: serviceKeys.detail(variables.serviceId) });
      toast.success(variables.isActive ? 'Service activated' : 'Service deactivated');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update service status');
    },
  });
};

// Duplicate service mutation
export const useDuplicateService = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (serviceId: number) => servicesApi.duplicateService(serviceId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      toast.success('Service duplicated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to duplicate service');
    },
  });
};

// Import services mutation
export const useImportServices = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (file: File) => servicesApi.importServices(file),
    onSuccess: (result: any) => {
      queryClient.invalidateQueries({ queryKey: serviceKeys.lists() });
      toast.success(`Successfully imported ${result.imported_count || 0} services`);
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to import services');
    },
  });
};

// Export services mutation
export const useExportServices = () => {
  return useMutation({
    mutationFn: (params?: Record<string, any>) => servicesApi.exportServices(params),
    onSuccess: (blob: Blob) => {
      // Create download link
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `services-export-${new Date().toISOString().split('T')[0]}.csv`;
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
      toast.success('Services exported successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to export services');
    },
  });
};
