import axios, { AxiosResponse, AxiosError } from 'axios';
import { toast } from 'react-hot-toast';
import { AuthTokens, LoginResponse, User, ApiError } from '@/types/api';

const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL || 'http://localhost:8000/api';

// Create axios instance
export const apiClient = axios.create({
  baseURL: API_BASE_URL,
  headers: {
    'Content-Type': 'application/json',
  },
  timeout: 30000, // 30 seconds
});

// Token management
export const getTokens = (): AuthTokens => {
  if (typeof window === 'undefined') return { access: '', refresh: '' };

  const access = localStorage.getItem('access_token') || '';
  const refresh = localStorage.getItem('refresh_token') || '';
  return { access, refresh };
};

export const setTokens = (tokens: AuthTokens): void => {
  if (typeof window === 'undefined') return;

  localStorage.setItem('access_token', tokens.access);
  localStorage.setItem('refresh_token', tokens.refresh);
};

export const clearTokens = (): void => {
  if (typeof window === 'undefined') return;

  localStorage.removeItem('access_token');
  localStorage.removeItem('refresh_token');
};

// Request interceptor to add auth token
apiClient.interceptors.request.use(
  (config) => {
    const { access } = getTokens();
    if (access) {
      config.headers.Authorization = `Bearer ${access}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor for error handling and token refresh
apiClient.interceptors.response.use(
  (response: AxiosResponse) => {
    return response;
  },
  async (error: AxiosError) => {
    const originalRequest = error.config as any;

    if (error.response?.status === 401 && !originalRequest._retry) {
      originalRequest._retry = true;

      const { refresh } = getTokens();
      if (refresh) {
        try {
          const response = await axios.post(`${API_BASE_URL}/auth/token/refresh/`, {
            refresh,
          });

          const newTokens = {
            access: response.data.access,
            refresh: refresh,
          };

          setTokens(newTokens);
          originalRequest.headers.Authorization = `Bearer ${response.data.access}`;

          return apiClient(originalRequest);
        } catch (refreshError) {
          clearTokens();
          if (typeof window !== 'undefined') {
            window.location.href = '/auth/login';
          }
          return Promise.reject(refreshError);
        }
      } else {
        clearTokens();
        if (typeof window !== 'undefined') {
          window.location.href = '/auth/login';
        }
      }
    }

    // Handle other errors
    if (error.response?.status >= 500) {
      toast.error('Server error. Please try again later.');
    } else if (error.response?.status === 403) {
      toast.error('Access denied. You do not have permission to perform this action.');
    } else if (error.response?.status === 404) {
      toast.error('Resource not found.');
    }

    return Promise.reject(error);
  }
);

// Helper function to handle API responses
const handleApiResponse = <T>(response: AxiosResponse<T>): T => {
  return response.data;
};

// Helper function to handle API errors
const handleApiError = (error: AxiosError): never => {
  const apiError: ApiError = {
    message: (error.response?.data as any)?.message ||
             (error.response?.data as any)?.error ||
             error.message ||
             'An error occurred',
    details: (error.response?.data as any)?.details || {},
    status: error.response?.status || 0,
  };
  throw apiError;
};

// Authentication API
export const authApi = {
  // Staff login with email/password
  loginStaff: async (credentials: { email: string; password: string }): Promise<LoginResponse> => {
    try {
      const response = await apiClient.post<LoginResponse>('/auth/login/email/', credentials);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user profile
  getProfile: async (): Promise<User> => {
    try {
      const response = await apiClient.get<User>('/auth/profile/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update user profile
  updateProfile: async (data: Partial<User>): Promise<User> => {
    try {
      const response = await apiClient.put<User>('/auth/profile/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Change password
  changePassword: async (data: { old_password: string; new_password: string; confirm_password: string }): Promise<void> => {
    try {
      const response = await apiClient.post('/auth/change-password/', data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Logout
  logout: async (): Promise<void> => {
    try {
      const response = await apiClient.post('/auth/logout/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Orders API
export const ordersApi = {
  // Get all orders (staff can see all)
  getOrders: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/orders/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get order details
  getOrderDetail: async (orderNumber: string) => {
    try {
      const response = await apiClient.get(`/orders/${orderNumber}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update order status
  updateOrderStatus: async (orderNumber: string, data: { status: string; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/status/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Assign provider to order
  assignProvider: async (orderNumber: string, data: { provider_id: number; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/assign-provider/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Cancel order
  cancelOrder: async (orderNumber: string, data: { reason: string; admin_notes?: string }) => {
    try {
      const response = await apiClient.post(`/orders/${orderNumber}/cancel/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Users API
export const usersApi = {
  // Get all users
  getUsers: async (params?: Record<string, any>) => {
    try {
      const response = await apiClient.get('/auth/users/', { params });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user details
  getUserDetail: async (userId: number) => {
    try {
      const response = await apiClient.get(`/auth/users/${userId}/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Update user
  updateUser: async (userId: number, data: Partial<User>) => {
    try {
      const response = await apiClient.put(`/auth/users/${userId}/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Lock/unlock user
  toggleUserLock: async (userId: number, data: { is_locked: boolean; lockout_duration?: number }) => {
    try {
      const response = await apiClient.post(`/auth/users/${userId}/toggle-lock/`, data);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Bulk user operations
  bulkUpdateUsers: async (userIds: number[], data: Partial<User>) => {
    try {
      const response = await apiClient.post('/auth/users/bulk-update/', {
        user_ids: userIds,
        ...data
      });
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Unlock user account
  unlockUser: async (userId: number) => {
    try {
      const response = await apiClient.post(`/auth/users/${userId}/unlock/`);
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Providers API
export const providersApi = {
  // Get all providers
  getProviders: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/providers/${query}`);
  },

  // Get provider details
  getProviderDetail: (providerId: number) =>
    apiRequest(`/providers/${providerId}/`),

  // Update provider verification status
  updateProviderVerification: (providerId: number, data: { verification_status: string; admin_notes?: string }) =>
    apiRequest(`/providers/${providerId}/verification/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Toggle provider availability
  toggleProviderAvailability: (providerId: number, data: { is_available: boolean }) =>
    apiRequest(`/providers/${providerId}/availability/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Services API
export const servicesApi = {
  // Get all services
  getServices: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/services/${query}`);
  },

  // Get service details
  getServiceDetail: (serviceId: number) =>
    apiRequest(`/catalogue/services/${serviceId}/`),

  // Create service
  createService: (data: any) =>
    apiRequest('/catalogue/services/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update service
  updateService: (serviceId: number, data: any) =>
    apiRequest(`/catalogue/services/${serviceId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete service
  deleteService: (serviceId: number) =>
    apiRequest(`/catalogue/services/${serviceId}/`, {
      method: 'DELETE',
    }),
};

// Categories API
export const categoriesApi = {
  // Get all categories
  getCategories: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/catalogue/categories/${query}`);
  },

  // Get category details
  getCategoryDetail: (categoryId: number) =>
    apiRequest(`/catalogue/categories/${categoryId}/`),

  // Create category
  createCategory: (data: any) =>
    apiRequest('/catalogue/categories/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update category
  updateCategory: (categoryId: number, data: any) =>
    apiRequest(`/catalogue/categories/${categoryId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete category
  deleteCategory: (categoryId: number) =>
    apiRequest(`/catalogue/categories/${categoryId}/`, {
      method: 'DELETE',
    }),
};

// Payments API
export const paymentsApi = {
  // Get all transactions
  getTransactions: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/payments/transactions/${query}`);
  },

  // Get transaction details
  getTransactionDetail: (transactionId: string) =>
    apiRequest(`/payments/transactions/${transactionId}/`),

  // Process refund
  processRefund: (transactionId: string, data: { amount?: string; reason: string }) =>
    apiRequest(`/payments/transactions/${transactionId}/refund/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};

// Analytics API
export const analyticsApi = {
  // Get dashboard stats (using orders dashboard endpoint)
  getDashboardStats: async () => {
    try {
      const response = await apiClient.get('/orders/dashboard/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },

  // Get user statistics (using auth admin endpoint)
  getUserStats: async () => {
    try {
      const response = await apiClient.get('/auth/admin/user-stats/');
      return handleApiResponse(response);
    } catch (error) {
      return handleApiError(error as AxiosError);
    }
  },
};

// Scheduling API
export const schedulingApi = {
  // Get time slots
  getTimeSlots: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/slots/${query}`);
  },

  // Create time slot
  createTimeSlot: (data: any) =>
    apiRequest('/scheduling/slots/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update time slot
  updateTimeSlot: (slotId: number, data: any) =>
    apiRequest(`/scheduling/slots/${slotId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Block time slot
  blockTimeSlot: (slotId: number, data: { reason: string; blocked_by: string }) =>
    apiRequest(`/scheduling/slots/${slotId}/block/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Get slot bookings
  getSlotBookings: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/scheduling/bookings/${query}`);
  },
};

// Coupons API
export const couponsApi = {
  // Get all coupons
  getCoupons: (params?: Record<string, any>) => {
    const query = params ? `?${new URLSearchParams(params).toString()}` : '';
    return apiRequest(`/coupons/${query}`);
  },

  // Get coupon details
  getCouponDetail: (couponId: number) =>
    apiRequest(`/coupons/${couponId}/`),

  // Create coupon
  createCoupon: (data: any) =>
    apiRequest('/coupons/', {
      method: 'POST',
      body: JSON.stringify(data),
    }),

  // Update coupon
  updateCoupon: (couponId: number, data: any) =>
    apiRequest(`/coupons/${couponId}/`, {
      method: 'PUT',
      body: JSON.stringify(data),
    }),

  // Delete coupon
  deleteCoupon: (couponId: number) =>
    apiRequest(`/coupons/${couponId}/`, {
      method: 'DELETE',
    }),

  // Toggle coupon status
  toggleCouponStatus: (couponId: number, data: { is_active: boolean }) =>
    apiRequest(`/coupons/${couponId}/toggle/`, {
      method: 'POST',
      body: JSON.stringify(data),
    }),
};
