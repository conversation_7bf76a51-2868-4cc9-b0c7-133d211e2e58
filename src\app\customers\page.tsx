'use client';

import React, { useState, useMemo } from 'react';
import {
  UserPlusIcon,
  EyeIcon,
  PencilIcon,
  CheckCircleIcon,
  XCircleIcon,
  UserIcon,
  EnvelopeIcon,
  PhoneIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import { useUsers, useUpdateUser, useBulkUpdateUsers } from '@/hooks/useUsers';
import { formatDateTime, formatRelativeTime } from '@/lib/utils';
import { User } from '@/types/api';

export default function CustomersPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [verificationFilter, setVerificationFilter] = useState('');
  const [page, setPage] = useState(1);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      user_type: 'CUSTOMER',
      ordering: '-date_joined',
      limit: 20,
      offset: (page - 1) * 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (statusFilter) {
      params.is_active = statusFilter === 'active';
    }

    if (verificationFilter) {
      params.is_verified = verificationFilter === 'verified';
    }

    return params;
  }, [searchTerm, statusFilter, verificationFilter, page]);

  // Use React Query hooks
  const { data: usersResponse, isLoading, error } = useUsers(queryParams);
  const updateUserMutation = useUpdateUser();
  const bulkUpdateMutation = useBulkUpdateUsers();

  const customers = usersResponse?.results || [];
  const totalItems = usersResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1); // Reset to first page
  };

  // Handle user status toggle
  const handleToggleStatus = (user: User) => {
    updateUserMutation.mutate({
      userId: user.id,
      data: { is_active: !user.is_active }
    });
  };

  // Handle user verification toggle
  const handleToggleVerification = (user: User) => {
    updateUserMutation.mutate({
      userId: user.id,
      data: { is_verified: !user.is_verified }
    });
  };

  // Define table columns
  const columns: Column<User>[] = [
    {
      key: 'name',
      label: 'Customer',
      sortable: true,
      render: (_, user) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {user.profile_picture ? (
              <img
                src={user.profile_picture}
                alt={user.name}
                className="w-8 h-8 rounded-full"
              />
            ) : (
              <div className="w-8 h-8 bg-gray-200 rounded-full flex items-center justify-center">
                <UserIcon className="w-4 h-4 text-gray-500" />
              </div>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">{user.name}</div>
            <div className="text-sm text-gray-500">ID: {user.id}</div>
          </div>
        </div>
      ),
    },
    {
      key: 'email',
      label: 'Contact',
      render: (_, user) => (
        <div className="space-y-1">
          {user.email && (
            <div className="flex items-center text-sm text-gray-900">
              <EnvelopeIcon className="w-4 h-4 mr-2 text-gray-400" />
              {user.email}
            </div>
          )}
          {user.mobile_number && (
            <div className="flex items-center text-sm text-gray-500">
              <PhoneIcon className="w-4 h-4 mr-2 text-gray-400" />
              {user.mobile_number}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'is_verified',
      label: 'Verification',
      render: (value) => (
        <Badge variant={value ? 'success' : 'warning'}>
          {value ? 'Verified' : 'Unverified'}
        </Badge>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value, user) => (
        <div className="flex items-center space-x-2">
          <Badge variant={value ? 'success' : 'secondary'}>
            {value ? 'Active' : 'Inactive'}
          </Badge>
          {user.is_locked && (
            <Badge variant="danger">Locked</Badge>
          )}
        </div>
      ),
    },
    {
      key: 'date_joined',
      label: 'Joined',
      sortable: true,
      render: (value) => (
        <div>
          <div className="text-sm text-gray-900">{formatDateTime(value)}</div>
          <div className="text-xs text-gray-500">{formatRelativeTime(value)}</div>
        </div>
      ),
    },
  ];

  // Define bulk actions
  const bulkActions = {
    selectedItems: [],
    onSelectionChange: (items: User[]) => {
      // Handle selection change
    },
    actions: [
      {
        label: 'Activate',
        onClick: (users: User[]) => {
          bulkUpdateMutation.mutate({
            userIds: users.map(u => u.id),
            data: { is_active: true }
          });
        },
        variant: 'primary' as const,
      },
      {
        label: 'Deactivate',
        onClick: (users: User[]) => {
          bulkUpdateMutation.mutate({
            userIds: users.map(u => u.id),
            data: { is_active: false }
          });
        },
        variant: 'secondary' as const,
      },
      {
        label: 'Verify',
        onClick: (users: User[]) => {
          bulkUpdateMutation.mutate({
            userIds: users.map(u => u.id),
            data: { is_verified: true }
          });
        },
        variant: 'primary' as const,
      },
    ],
  };

  // Define row actions
  const rowActions = (user: User) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button variant="ghost" size="sm" title="Edit Customer">
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleStatus(user)}
        title={user.is_active ? 'Deactivate' : 'Activate'}
      >
        {user.is_active ? (
          <XCircleIcon className="w-4 h-4 text-red-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleVerification(user)}
        title={user.is_verified ? 'Unverify' : 'Verify'}
      >
        {user.is_verified ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-blue-500" />
        )}
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Customer Management</h1>
          <Button>
            <UserPlusIcon className="w-4 h-4 mr-2" />
            Add Customer
          </Button>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Verification
              </label>
              <select
                value={verificationFilter}
                onChange={(e) => {
                  setVerificationFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All</option>
                <option value="verified">Verified</option>
                <option value="unverified">Unverified</option>
              </select>
            </div>
          </div>
        </div>

        {/* Customers Table */}
        <DataTable
          data={customers}
          columns={columns}
          loading={isLoading}
          error={error ? (error as any).message : undefined}
          searchable
          searchPlaceholder="Search customers by name, email, or phone..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          bulkActions={bulkActions}
          emptyState={{
            title: 'No customers found',
            description: 'No customers match your current filters.',
            action: (
              <Button onClick={() => {
                setSearchTerm('');
                setStatusFilter('');
                setVerificationFilter('');
                setPage(1);
              }}>
                Clear Filters
              </Button>
            ),
          }}
        />
      </div>
    </DashboardLayout>
  );
}
