'use client';

import React, { useState, useEffect } from 'react';
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { XMarkIcon, PhotoIcon } from '@heroicons/react/24/outline';
import { Button } from '@/components/ui/Button';
import { FormField, Input, Textarea, Select, Checkbox, FileUpload } from '@/components/ui/Form';
import { useCreateService, useUpdateService } from '@/hooks/useServices';
import { useCategories } from '@/hooks/useCategories';
import { serviceSchema, ServiceFormData } from '@/lib/validations';
import { Service } from '@/types/api';

interface ServiceFormProps {
  service?: Service;
  isOpen: boolean;
  onClose: () => void;
  onSuccess?: () => void;
}

export default function ServiceForm({ service, isOpen, onClose, onSuccess }: ServiceFormProps) {
  const [imagePreview, setImagePreview] = useState<string | null>(null);
  const [imageFile, setImageFile] = useState<File | null>(null);

  const isEditing = !!service;
  const createServiceMutation = useCreateService();
  const updateServiceMutation = useUpdateService();
  const { data: categoriesResponse } = useCategories({ limit: 100 });

  const categories = categoriesResponse?.results || [];

  const {
    register,
    handleSubmit,
    formState: { errors, isSubmitting },
    reset,
    watch,
    setValue,
  } = useForm<ServiceFormData>({
    resolver: zodResolver(serviceSchema),
    defaultValues: {
      title: '',
      category: 0,
      description: '',
      base_price: '',
      discount_price: '',
      time_to_complete: '',
      is_active: true,
      requires_partial_payment: false,
      partial_payment_type: 'percentage',
      partial_payment_value: '',
      partial_payment_description: '',
    },
  });

  const requiresPartialPayment = watch('requires_partial_payment');

  // Reset form when service changes
  useEffect(() => {
    if (service) {
      reset({
        title: service.title,
        category: service.category,
        description: service.description,
        base_price: service.base_price.toString(),
        discount_price: service.discount_price?.toString() || '',
        time_to_complete: service.time_to_complete,
        is_active: service.is_active,
        requires_partial_payment: service.requires_partial_payment,
        partial_payment_type: service.partial_payment_type || 'percentage',
        partial_payment_value: service.partial_payment_value?.toString() || '',
        partial_payment_description: service.partial_payment_description || '',
      });
      setImagePreview(service.image || null);
    } else {
      reset();
      setImagePreview(null);
      setImageFile(null);
    }
  }, [service, reset]);

  const handleImageSelect = (files: FileList | null) => {
    if (files && files[0]) {
      const file = files[0];
      setImageFile(file);
      
      // Create preview
      const reader = new FileReader();
      reader.onload = (e) => {
        setImagePreview(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  const onSubmit = async (data: ServiceFormData) => {
    try {
      const formData = new FormData();
      
      // Add form fields
      Object.entries(data).forEach(([key, value]) => {
        if (value !== null && value !== undefined && value !== '') {
          formData.append(key, value.toString());
        }
      });

      // Add image if selected
      if (imageFile) {
        formData.append('image', imageFile);
      }

      if (isEditing && service) {
        await updateServiceMutation.mutateAsync({
          serviceId: service.id,
          data: formData,
        });
      } else {
        await createServiceMutation.mutateAsync(formData);
      }

      onSuccess?.();
      onClose();
    } catch (error) {
      // Error is handled by the mutation
    }
  };

  if (!isOpen) return null;

  return (
    <div className="fixed inset-0 z-50 overflow-y-auto">
      <div className="flex min-h-screen items-center justify-center p-4">
        <div className="fixed inset-0 bg-black bg-opacity-25" onClick={onClose} />
        
        <div className="relative bg-white rounded-lg shadow-xl max-w-2xl w-full max-h-[90vh] overflow-y-auto">
          <div className="flex items-center justify-between p-6 border-b">
            <h2 className="text-xl font-semibold text-gray-900">
              {isEditing ? 'Edit Service' : 'Create New Service'}
            </h2>
            <button
              onClick={onClose}
              className="text-gray-400 hover:text-gray-600"
            >
              <XMarkIcon className="w-6 h-6" />
            </button>
          </div>

          <form onSubmit={handleSubmit(onSubmit)} className="p-6 space-y-6">
            {/* Basic Information */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Basic Information</h3>
              
              <FormField
                label="Service Title"
                required
                error={errors.title}
              >
                <Input
                  {...register('title')}
                  placeholder="Enter service title"
                  error={!!errors.title}
                />
              </FormField>

              <FormField
                label="Category"
                required
                error={errors.category}
              >
                <Select
                  {...register('category', { valueAsNumber: true })}
                  options={categories.map(cat => ({
                    value: cat.id,
                    label: cat.name
                  }))}
                  placeholder="Select a category"
                  error={!!errors.category}
                />
              </FormField>

              <FormField
                label="Description"
                required
                error={errors.description}
              >
                <Textarea
                  {...register('description')}
                  placeholder="Describe the service in detail"
                  rows={4}
                  error={!!errors.description}
                />
              </FormField>
            </div>

            {/* Pricing */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Pricing</h3>
              
              <div className="grid grid-cols-2 gap-4">
                <FormField
                  label="Base Price (₹)"
                  required
                  error={errors.base_price}
                >
                  <Input
                    {...register('base_price')}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    error={!!errors.base_price}
                  />
                </FormField>

                <FormField
                  label="Discount Price (₹)"
                  error={errors.discount_price}
                >
                  <Input
                    {...register('discount_price')}
                    type="number"
                    step="0.01"
                    placeholder="0.00"
                    error={!!errors.discount_price}
                  />
                </FormField>
              </div>

              <FormField
                label="Time to Complete"
                required
                error={errors.time_to_complete}
              >
                <Input
                  {...register('time_to_complete')}
                  placeholder="e.g., 2 hours, 1 day, 30 minutes"
                  error={!!errors.time_to_complete}
                />
              </FormField>
            </div>

            {/* Partial Payment */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Payment Options</h3>
              
              <FormField error={errors.requires_partial_payment}>
                <Checkbox
                  {...register('requires_partial_payment')}
                  label="Requires Partial Payment"
                  description="Enable if this service requires advance payment"
                />
              </FormField>

              {requiresPartialPayment && (
                <div className="ml-6 space-y-4 border-l-2 border-gray-200 pl-4">
                  <div className="grid grid-cols-2 gap-4">
                    <FormField
                      label="Payment Type"
                      error={errors.partial_payment_type}
                    >
                      <Select
                        {...register('partial_payment_type')}
                        options={[
                          { value: 'percentage', label: 'Percentage' },
                          { value: 'fixed', label: 'Fixed Amount' }
                        ]}
                        error={!!errors.partial_payment_type}
                      />
                    </FormField>

                    <FormField
                      label="Payment Value"
                      error={errors.partial_payment_value}
                    >
                      <Input
                        {...register('partial_payment_value')}
                        type="number"
                        step="0.01"
                        placeholder="Enter value"
                        error={!!errors.partial_payment_value}
                      />
                    </FormField>
                  </div>

                  <FormField
                    label="Payment Description"
                    error={errors.partial_payment_description}
                  >
                    <Input
                      {...register('partial_payment_description')}
                      placeholder="e.g., 50% advance payment required"
                      error={!!errors.partial_payment_description}
                    />
                  </FormField>
                </div>
              )}
            </div>

            {/* Image Upload */}
            <div className="space-y-4">
              <h3 className="text-lg font-medium text-gray-900">Service Image</h3>
              
              {imagePreview ? (
                <div className="relative">
                  <img
                    src={imagePreview}
                    alt="Service preview"
                    className="w-full h-48 object-cover rounded-lg"
                  />
                  <button
                    type="button"
                    onClick={() => {
                      setImagePreview(null);
                      setImageFile(null);
                    }}
                    className="absolute top-2 right-2 bg-red-500 text-white rounded-full p-1 hover:bg-red-600"
                  >
                    <XMarkIcon className="w-4 h-4" />
                  </button>
                </div>
              ) : (
                <FileUpload
                  accept="image/*"
                  maxSize={5}
                  onFileSelect={handleImageSelect}
                />
              )}
            </div>

            {/* Status */}
            <div className="space-y-4">
              <FormField error={errors.is_active}>
                <Checkbox
                  {...register('is_active')}
                  label="Active"
                  description="Make this service available to customers"
                />
              </FormField>
            </div>

            {/* Form Actions */}
            <div className="flex items-center justify-end space-x-3 pt-6 border-t">
              <Button
                type="button"
                variant="outline"
                onClick={onClose}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button
                type="submit"
                disabled={isSubmitting}
              >
                {isSubmitting ? 'Saving...' : (isEditing ? 'Update Service' : 'Create Service')}
              </Button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
