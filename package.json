{"name": "nextjs_staff", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev -p 3002", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@headlessui/react": "^2.2.4", "clsx": "^2.1.1", "date-fns": "^4.1.0", "lucide-react": "^0.518.0", "next": "15.3.4", "react": "^19.0.0", "react-dom": "^19.0.0", "recharts": "^2.15.3"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.21", "eslint": "^9", "eslint-config-next": "15.3.4", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5"}}