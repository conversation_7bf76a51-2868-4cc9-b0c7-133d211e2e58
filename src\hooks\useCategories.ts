import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { categoriesApi } from '@/lib/api';
import { Category, PaginatedResponse } from '@/types/api';

// Query keys
export const categoryKeys = {
  all: ['categories'] as const,
  lists: () => [...categoryKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...categoryKeys.lists(), params] as const,
  tree: () => [...categoryKeys.all, 'tree'] as const,
  details: () => [...categoryKeys.all, 'detail'] as const,
  detail: (categoryId: number) => [...categoryKeys.details(), categoryId] as const,
};

// Get categories list with filters
export const useCategories = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: categoryKeys.list(params),
    queryFn: () => categoriesApi.getCategories(params),
    staleTime: 5 * 60 * 1000, // 5 minutes - categories change less frequently
  });
};

// Get category tree (hierarchical)
export const useCategoryTree = () => {
  return useQuery({
    queryKey: categoryKeys.tree(),
    queryFn: () => categoriesApi.getCategoryTree(),
    staleTime: 10 * 60 * 1000, // 10 minutes - tree structure changes infrequently
  });
};

// Get single category details
export const useCategory = (categoryId: number) => {
  return useQuery({
    queryKey: categoryKeys.detail(categoryId),
    queryFn: () => categoriesApi.getCategoryDetail(categoryId),
    enabled: !!categoryId,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
};

// Create category mutation
export const useCreateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: FormData | any) => categoriesApi.createCategory(data),
    onSuccess: () => {
      // Invalidate categories list and tree
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() });
      toast.success('Category created successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to create category');
    },
  });
};

// Update category mutation
export const useUpdateCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, data }: { categoryId: number; data: FormData | any }) =>
      categoriesApi.updateCategory(categoryId, data),
    onSuccess: (_, variables) => {
      // Invalidate categories list and tree
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() });
      // Invalidate specific category detail
      queryClient.invalidateQueries({ queryKey: categoryKeys.detail(variables.categoryId) });
      toast.success('Category updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update category');
    },
  });
};

// Delete category mutation
export const useDeleteCategory = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryId: number) => categoriesApi.deleteCategory(categoryId),
    onSuccess: (_, categoryId) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() });
      queryClient.removeQueries({ queryKey: categoryKeys.detail(categoryId) });
      toast.success('Category deleted successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to delete category');
    },
  });
};

// Reorder categories mutation
export const useReorderCategories = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (categoryOrders: Array<{ id: number; parent?: number; order: number }>) =>
      categoriesApi.reorderCategories(categoryOrders),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() });
      toast.success('Categories reordered successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to reorder categories');
    },
  });
};

// Toggle category status mutation
export const useToggleCategoryStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ categoryId, isActive }: { categoryId: number; isActive: boolean }) =>
      categoriesApi.toggleCategoryStatus(categoryId, isActive),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: categoryKeys.lists() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.tree() });
      queryClient.invalidateQueries({ queryKey: categoryKeys.detail(variables.categoryId) });
      toast.success(variables.isActive ? 'Category activated' : 'Category deactivated');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update category status');
    },
  });
};
