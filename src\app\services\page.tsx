'use client';

import React, { useState, useMemo } from 'react';
import {
  PlusIcon,
  EyeIcon,
  PencilIcon,
  TrashIcon,
  DocumentDuplicateIcon,
  CheckCircleIcon,
  XCircleIcon,
  PhotoIcon,
  ArrowUpTrayIcon,
  ArrowDownTrayIcon
} from '@heroicons/react/24/outline';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { Button } from '@/components/ui/Button';
import { Badge } from '@/components/ui/Badge';
import DataTable, { Column } from '@/components/ui/DataTable';
import {
  useServices,
  useDeleteService,
  useBulkUpdateServices,
  useBulkDeleteServices,
  useToggleServiceStatus,
  useDuplicateService,
  useExportServices
} from '@/hooks/useServices';
import { useCategories } from '@/hooks/useCategories';
import { formatCurrency, formatDateTime } from '@/lib/utils';
import { Service } from '@/types/api';
import ServiceForm from '@/components/forms/ServiceForm';
import ApiDebugger from '@/components/debug/ApiDebugger';

export default function ServicesPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryFilter, setCategoryFilter] = useState('');
  const [statusFilter, setStatusFilter] = useState('');
  const [priceRangeFilter, setPriceRangeFilter] = useState('');
  const [page, setPage] = useState(1);
  const [showCreateModal, setShowCreateModal] = useState(false);
  const [editingService, setEditingService] = useState<Service | null>(null);

  // Prepare query parameters
  const queryParams = useMemo(() => {
    const params: Record<string, any> = {
      ordering: '-created_at',
      limit: 20,
      offset: (page - 1) * 20,
    };

    if (searchTerm) {
      params.search = searchTerm;
    }

    if (categoryFilter) {
      params.category = categoryFilter;
    }

    if (statusFilter) {
      params.is_active = statusFilter === 'active';
    }

    if (priceRangeFilter) {
      const [min, max] = priceRangeFilter.split('-');
      if (min) params.min_price = min;
      if (max) params.max_price = max;
    }

    return params;
  }, [searchTerm, categoryFilter, statusFilter, priceRangeFilter, page]);

  // Use React Query hooks
  const { data: servicesResponse, isLoading, error } = useServices(queryParams);
  const { data: categoriesResponse } = useCategories({ limit: 100 });
  const deleteServiceMutation = useDeleteService();
  const bulkUpdateMutation = useBulkUpdateServices();
  const bulkDeleteMutation = useBulkDeleteServices();
  const toggleStatusMutation = useToggleServiceStatus();
  const duplicateServiceMutation = useDuplicateService();
  const exportServicesMutation = useExportServices();

  const services = servicesResponse?.results || [];
  const categories = categoriesResponse?.results || [];
  const totalItems = servicesResponse?.count || 0;
  const totalPages = Math.ceil(totalItems / 20);

  // Handle search
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    setPage(1); // Reset to first page
  };

  // Handle service actions
  const handleDeleteService = (serviceId: number) => {
    if (confirm('Are you sure you want to delete this service?')) {
      deleteServiceMutation.mutate(serviceId);
    }
  };

  const handleToggleStatus = (service: Service) => {
    toggleStatusMutation.mutate({
      serviceId: service.id,
      isActive: !service.is_active
    });
  };

  const handleDuplicateService = (serviceId: number) => {
    duplicateServiceMutation.mutate(serviceId);
  };

  const handleExportServices = () => {
    exportServicesMutation.mutate(queryParams);
  };

  const handleEditService = (service: Service) => {
    setEditingService(service);
  };

  const handleCloseModal = () => {
    setShowCreateModal(false);
    setEditingService(null);
  };

  // Define table columns
  const columns: Column<Service>[] = [
    {
      key: 'title',
      label: 'Service',
      sortable: true,
      render: (_, service) => (
        <div className="flex items-center space-x-3">
          <div className="flex-shrink-0">
            {service.image ? (
              <img
                src={service.image}
                alt={service.title}
                className="w-10 h-10 rounded-lg object-cover"
              />
            ) : (
              <div className="w-10 h-10 bg-gray-200 rounded-lg flex items-center justify-center">
                <PhotoIcon className="w-5 h-5 text-gray-500" />
              </div>
            )}
          </div>
          <div>
            <div className="font-medium text-gray-900">{service.title}</div>
            <div className="text-sm text-gray-500">
              {service.category_name || 'No category'}
            </div>
          </div>
        </div>
      ),
    },
    {
      key: 'base_price',
      label: 'Pricing',
      sortable: true,
      render: (_, service) => (
        <div>
          <div className="font-medium text-gray-900">
            {formatCurrency(service.base_price)}
          </div>
          {service.discount_price && (
            <div className="text-sm text-green-600">
              Sale: {formatCurrency(service.discount_price)}
            </div>
          )}
        </div>
      ),
    },
    {
      key: 'time_to_complete',
      label: 'Duration',
      render: (value) => (
        <span className="text-sm text-gray-900">{value}</span>
      ),
    },
    {
      key: 'is_active',
      label: 'Status',
      render: (value) => (
        <Badge variant={value ? 'success' : 'secondary'}>
          {value ? 'Active' : 'Inactive'}
        </Badge>
      ),
    },
    {
      key: 'requires_partial_payment',
      label: 'Payment',
      render: (value) => (
        <Badge variant={value ? 'info' : 'secondary'}>
          {value ? 'Partial Payment' : 'Full Payment'}
        </Badge>
      ),
    },
    {
      key: 'created_at',
      label: 'Created',
      sortable: true,
      render: (value) => (
        <div className="text-sm text-gray-900">
          {formatDateTime(value)}
        </div>
      ),
    },
  ];

  // Define bulk actions
  const bulkActions = {
    selectedItems: [],
    onSelectionChange: (items: Service[]) => {
      // Handle selection change
    },
    actions: [
      {
        label: 'Activate',
        onClick: (services: Service[]) => {
          bulkUpdateMutation.mutate({
            serviceIds: services.map(s => s.id),
            data: { is_active: true }
          });
        },
        variant: 'primary' as const,
      },
      {
        label: 'Deactivate',
        onClick: (services: Service[]) => {
          bulkUpdateMutation.mutate({
            serviceIds: services.map(s => s.id),
            data: { is_active: false }
          });
        },
        variant: 'secondary' as const,
      },
      {
        label: 'Delete',
        onClick: (services: Service[]) => {
          if (confirm(`Are you sure you want to delete ${services.length} services?`)) {
            bulkDeleteMutation.mutate(services.map(s => s.id));
          }
        },
        variant: 'danger' as const,
      },
    ],
  };

  // Define row actions
  const rowActions = (service: Service) => (
    <div className="flex items-center space-x-2">
      <Button variant="ghost" size="sm" title="View Details">
        <EyeIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleEditService(service)}
        title="Edit Service"
      >
        <PencilIcon className="w-4 h-4" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleDuplicateService(service.id)}
        title="Duplicate Service"
      >
        <DocumentDuplicateIcon className="w-4 h-4 text-blue-500" />
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleToggleStatus(service)}
        title={service.is_active ? 'Deactivate' : 'Activate'}
      >
        {service.is_active ? (
          <XCircleIcon className="w-4 h-4 text-orange-500" />
        ) : (
          <CheckCircleIcon className="w-4 h-4 text-green-500" />
        )}
      </Button>
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleDeleteService(service.id)}
        title="Delete Service"
      >
        <TrashIcon className="w-4 h-4 text-red-500" />
      </Button>
    </div>
  );

  return (
    <DashboardLayout>
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h1 className="text-2xl font-bold text-gray-900">Service Management</h1>
          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              onClick={handleExportServices}
              disabled={exportServicesMutation.isPending}
            >
              <ArrowDownTrayIcon className="w-4 h-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => setShowCreateModal(true)}>
              <PlusIcon className="w-4 h-4 mr-2" />
              Add Service
            </Button>
          </div>
        </div>

        {/* Filters */}
        <div className="bg-white p-4 rounded-lg shadow space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Category
              </label>
              <select
                value={categoryFilter}
                onChange={(e) => {
                  setCategoryFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Categories</option>
                {categories.map((category) => (
                  <option key={category.id} value={category.id}>
                    {category.name}
                  </option>
                ))}
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Status
              </label>
              <select
                value={statusFilter}
                onChange={(e) => {
                  setStatusFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Status</option>
                <option value="active">Active</option>
                <option value="inactive">Inactive</option>
              </select>
            </div>

            <div>
              <label className="block text-sm font-medium text-gray-700 mb-1">
                Price Range
              </label>
              <select
                value={priceRangeFilter}
                onChange={(e) => {
                  setPriceRangeFilter(e.target.value);
                  setPage(1);
                }}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500"
              >
                <option value="">All Prices</option>
                <option value="0-100">₹0 - ₹100</option>
                <option value="100-500">₹100 - ₹500</option>
                <option value="500-1000">₹500 - ₹1000</option>
                <option value="1000-5000">₹1000 - ₹5000</option>
                <option value="5000-">₹5000+</option>
              </select>
            </div>
          </div>
        </div>

        {/* API Debugger - Temporary */}
        <ApiDebugger />

        {/* Services Table */}
        <DataTable
          data={services}
          columns={columns}
          loading={isLoading}
          error={error ? (error as any).message : undefined}
          searchable
          searchPlaceholder="Search services by title, description, or category..."
          onSearch={handleSearch}
          sortable
          pagination={{
            page,
            totalPages,
            totalItems,
            itemsPerPage: 20,
            onPageChange: setPage,
          }}
          actions={rowActions}
          bulkActions={bulkActions}
          emptyState={{
            title: 'No services found',
            description: 'No services match your current filters.',
            action: (
              <div className="space-y-3">
                <Button onClick={() => {
                  setSearchTerm('');
                  setCategoryFilter('');
                  setStatusFilter('');
                  setPriceRangeFilter('');
                  setPage(1);
                }}>
                  Clear Filters
                </Button>
                <div className="text-sm text-gray-500">or</div>
                <Button onClick={() => setShowCreateModal(true)}>
                  <PlusIcon className="w-4 h-4 mr-2" />
                  Create First Service
                </Button>
              </div>
            ),
          }}
        />

        {/* Service Form Modal */}
        <ServiceForm
          service={editingService}
          isOpen={showCreateModal || !!editingService}
          onClose={handleCloseModal}
          onSuccess={handleCloseModal}
        />
      </div>
    </DashboardLayout>
  );
}
