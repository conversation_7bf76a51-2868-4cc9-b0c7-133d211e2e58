import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { providersApi } from '@/lib/api';
import { Provider, PaginatedResponse } from '@/types/api';

// Query keys
export const providerKeys = {
  all: ['providers'] as const,
  lists: () => [...providerKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...providerKeys.lists(), params] as const,
  details: () => [...providerKeys.all, 'detail'] as const,
  detail: (providerId: number) => [...providerKeys.details(), providerId] as const,
};

// Get providers list with filters
export const useProviders = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: providerKeys.list(params),
    queryFn: () => providersApi.getProviders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single provider details
export const useProvider = (providerId: number) => {
  return useQuery({
    queryKey: providerKeys.detail(providerId),
    queryFn: () => providersApi.getProviderDetail(providerId),
    enabled: !!providerId,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Update provider verification mutation
export const useUpdateProviderVerification = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: { verification_status: string; admin_notes?: string } }) =>
      providersApi.updateProviderVerification(providerId, data),
    onSuccess: (_, variables) => {
      // Invalidate and refetch providers list
      queryClient.invalidateQueries({ queryKey: providerKeys.lists() });
      // Invalidate specific provider detail
      queryClient.invalidateQueries({ queryKey: providerKeys.detail(variables.providerId) });
      toast.success('Provider verification updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update provider verification');
    },
  });
};

// Toggle provider availability mutation
export const useToggleProviderAvailability = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ providerId, data }: { providerId: number; data: { is_available: boolean } }) =>
      providersApi.toggleProviderAvailability(providerId, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: providerKeys.lists() });
      queryClient.invalidateQueries({ queryKey: providerKeys.detail(variables.providerId) });
      toast.success(variables.data.is_available ? 'Provider enabled successfully' : 'Provider disabled successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update provider availability');
    },
  });
};
