import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { toast } from 'react-hot-toast';
import { ordersApi } from '@/lib/api';
import { Order, PaginatedResponse } from '@/types/api';

// Query keys
export const orderKeys = {
  all: ['orders'] as const,
  lists: () => [...orderKeys.all, 'list'] as const,
  list: (params: Record<string, any>) => [...orderKeys.lists(), params] as const,
  details: () => [...orderKeys.all, 'detail'] as const,
  detail: (orderNumber: string) => [...orderKeys.details(), orderNumber] as const,
};

// Get orders list with filters
export const useOrders = (params: Record<string, any> = {}) => {
  return useQuery({
    queryKey: orderKeys.list(params),
    queryFn: () => ordersApi.getOrders(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

// Get single order details
export const useOrder = (orderNumber: string) => {
  return useQuery({
    queryKey: orderKeys.detail(orderNumber),
    queryFn: () => ordersApi.getOrderDetail(orderNumber),
    enabled: !!orderNumber,
    staleTime: 1 * 60 * 1000, // 1 minute
  });
};

// Update order status mutation
export const useUpdateOrderStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderNumber, data }: { orderNumber: string; data: { status: string; admin_notes?: string } }) =>
      ordersApi.updateOrderStatus(orderNumber, data),
    onSuccess: (_, variables) => {
      // Invalidate and refetch orders list
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      // Invalidate specific order detail
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(variables.orderNumber) });
      toast.success('Order status updated successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to update order status');
    },
  });
};

// Assign provider to order mutation
export const useAssignProvider = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderNumber, data }: { orderNumber: string; data: { provider_id: number; admin_notes?: string } }) =>
      ordersApi.assignProvider(orderNumber, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(variables.orderNumber) });
      toast.success('Provider assigned successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to assign provider');
    },
  });
};

// Cancel order mutation
export const useCancelOrder = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ orderNumber, data }: { orderNumber: string; data: { reason: string; admin_notes?: string } }) =>
      ordersApi.cancelOrder(orderNumber, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: orderKeys.lists() });
      queryClient.invalidateQueries({ queryKey: orderKeys.detail(variables.orderNumber) });
      toast.success('Order cancelled successfully');
    },
    onError: (error: any) => {
      toast.error(error.message || 'Failed to cancel order');
    },
  });
};
